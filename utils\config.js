// 微信小程序配置
// const BASE_URL = "https://www.ylst-etc.cn/api";  
const BASE_URL = "https://drtjza50.beesnat.com";

// 环境判断
const isProduction = process.env.NODE_ENV === "production";
const isDevelopment = !isProduction;

// API超时配置
const API_TIMEOUT = 15000;

// ===== 应用基本信息配置 =====
const APP_CONFIG = {
  // 应用名称
  APP_NAME: "一路畅通",
  // 应用版本
  VERSION_NAME: "1.0.3",
  VERSION_CODE: 102,
  // 开发公司
  COMPANY_NAME: "",
};

// ===== 微信小程序配置 =====
const WECHAT_CONFIG = {
  // 微信小程序AppID (manifest.json中使用)
  MINI_PROGRAM_APPID: "wxaefdb39f5390ee48",
  // 项目配置AppID (project.config.json中使用)
  PROJECT_APPID: "wxaefdb39f5390ee48",
};

// ===== 客服配置 =====
const CUSTOMER_SERVICE_CONFIG = {
  // 客服电话
  PHONE_NUMBER: "4000067882",
  // 企业微信配置
  WEWORK_CONFIG: {
    // 企业微信ID
    CORP_ID: "wwd0d81b5af0714ec1",
    // 客服ID URL
    CUSTOMER_SERVICE_URL: "https://work.weixin.qq.com/kfid/kfcf0ae369302fb0824",
  },
};

// ===== 第三方小程序跳转配置 =====
const MINI_PROGRAM_JUMP_CONFIG = {
  // 快递小程序
  EXPRESS_MINI_PROGRAM: {
    APP_ID: "wxddb3eb32425e4a96",
    ENV_VERSION: "release",
    MERCHANT_CODE: "PPVMZIYXEXGDRCZOG",
    SHOP_ID: "1303036541465796608",
  },
  // 充电小程序
  CHARGING_MINI_PROGRAM: {
    APP_ID: "wx01440835f1987e8b",
    ENV_VERSION: "release",
    PATH: "pages/index/index",
  },
  // 加油小程序
  OIL_MINI_PROGRAM: {
    APP_ID: "wx1f1ea04b716771be",
    ENV_VERSION: "release",
    PATH: "pages/index",
  },
};

// ===== 手动配置说明 =====
// 以下配置需要手动更新到对应的JSON文件中：
// 1. manifest.json:
//    - name: APP_CONFIG.APP_NAME
//    - versionName: APP_CONFIG.VERSION_NAME
//    - versionCode: APP_CONFIG.VERSION_CODE
//    - mp-weixin.appid: WECHAT_CONFIG.MINI_PROGRAM_APPID
// 2. project.config.json:
//    - appid: WECHAT_CONFIG.PROJECT_APPID
// 3. utils/auth.js:
//    - APP_VERSION: APP_CONFIG.VERSION_NAME

export default {
  BASE_URL,
  isProduction,
  isDevelopment,
  API_TIMEOUT,
  DEBUG: isDevelopment,
  APP_CONFIG,
  WECHAT_CONFIG,
  CUSTOMER_SERVICE_CONFIG,
  MINI_PROGRAM_JUMP_CONFIG,
};

export { BASE_URL, isProduction, isDevelopment, API_TIMEOUT };
